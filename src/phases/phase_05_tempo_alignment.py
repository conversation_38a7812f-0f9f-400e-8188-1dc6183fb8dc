"""
Phase 5: Tempo Alignment & BPM Validation

This phase aligns detected beats with expected BPM from TJA files and validates
tempo consistency across segments. It provides the precise timing foundation
needed for accurate note generation.

Key Features:
- Tempo alignment using optimization algorithms
- BPM validation against TJA references
- Tempo change detection
- Robust error handling and logging
- Comprehensive alignment reports
"""

import json
import logging
import numpy as np
import scipy.optimize
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from sklearn.linear_model import RANSACRegressor
import time
import traceback


class TempoAlignmentProcessor:
    """Main processor for Phase 5 tempo alignment and BPM validation."""
    
    def __init__(self, 
                 input_dir: Path = Path("data/processed/phase4/outputs"),
                 output_dir: Path = Path("data/processed/phase5"),
                 tja_data_dir: Path = Path("data/raw/ese"),
                 bpm_tolerance: float = 0.05):
        """
        Initialize the tempo alignment processor.
        
        Args:
            input_dir: Directory containing Phase 4 outputs
            output_dir: Directory for Phase 5 outputs
            tja_data_dir: Directory containing TJA files
            bmp_tolerance: Acceptable BPM error percentage (default 5%)
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.tja_data_dir = Path(tja_data_dir)
        self.bpm_tolerance = bpm_tolerance
        
        # Setup logging
        self.setup_logging()
        
        # Create output directories
        self.setup_output_directories()
        
        # Statistics tracking
        self.stats = {
            "total_songs": 0,
            "processed_songs": 0,
            "validation_passed": 0,
            "validation_failed": 0,
            "avg_bpm_error": 0.0,
            "avg_tempo_drift": 0.0,
            "processing_errors": []
        }
    
    def setup_logging(self):
        """Setup logging for the processor."""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"phase05_{int(time.time())}.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_output_directories(self):
        """Create all necessary output directories."""
        directories = [
            "tempo_alignment",
            "bpm_validation", 
            "aligned_beats",
            "timing_analysis",
            "visualizations"
        ]
        
        for dir_name in directories:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Created output directories in {self.output_dir}")
    
    def load_tja_metadata(self, song_name: str) -> Tuple[Optional[float], Optional[float]]:
        """
        Load BPM and offset from TJA file.

        Args:
            song_name: Name of the song to find TJA file for

        Returns:
            Tuple of (bpm, offset) or (None, None) if not found
        """
        try:
            # Build TJA metadata cache if not exists
            if not hasattr(self, '_tja_cache'):
                self._build_tja_cache()

            # Look up in cache
            if song_name in self._tja_cache:
                return self._tja_cache[song_name]

            self.logger.warning(f"No TJA file found for {song_name}")
            return None, None

        except Exception as e:
            self.logger.error(f"Error loading TJA metadata for {song_name}: {e}")
            return None, None

    def _build_tja_cache(self):
        """Build cache of TJA metadata for faster lookup."""
        self._tja_cache = {}
        self.logger.info("Building TJA metadata cache...")

        try:
            # Find all TJA files
            tja_files = list(self.tja_data_dir.rglob("*.tja"))

            for tja_file in tja_files:
                try:
                    song_name = tja_file.stem

                    # Parse TJA file for BPM and OFFSET
                    with open(tja_file, 'r', encoding='utf-8-sig') as f:
                        content = f.read()

                    bpm = None
                    offset = None

                    for line in content.split('\n'):
                        line = line.strip()
                        if line.startswith('BPM:'):
                            bpm = float(line.split(':')[1])
                        elif line.startswith('OFFSET:'):
                            offset = float(line.split(':')[1])

                    if bpm is not None:
                        self._tja_cache[song_name] = (bpm, offset or 0.0)

                except Exception as e:
                    self.logger.warning(f"Error parsing TJA file {tja_file}: {e}")
                    continue

            self.logger.info(f"Built TJA cache with {len(self._tja_cache)} songs")

        except Exception as e:
            self.logger.error(f"Error building TJA cache: {e}")
            self._tja_cache = {}
    
    def robust_tempo_estimation(self, beat_times: np.ndarray) -> float:
        """
        Estimate tempo robustly using RANSAC to handle outliers.
        
        Args:
            beat_times: Array of beat times
            
        Returns:
            Estimated BPM
        """
        if len(beat_times) < 4:
            return 120.0  # Default BPM
        
        try:
            # Prepare data for linear regression
            X = beat_times[:-1].reshape(-1, 1)
            y = np.diff(beat_times)
            
            # Use RANSAC to handle outliers
            ransac = RANSACRegressor(random_state=42)
            ransac.fit(X, y)
            
            # Convert interval to BPM
            estimated_interval = np.mean(ransac.predict(X))
            return 60.0 / estimated_interval if estimated_interval > 0 else 120.0
            
        except Exception as e:
            self.logger.warning(f"Error in robust tempo estimation: {e}")
            return 120.0
    
    def detect_tempo_changes(self, beat_times: np.ndarray, base_bpm: float, 
                           window_size: int = 8) -> List[Dict]:
        """
        Detect tempo changes using sliding window analysis.
        
        Args:
            beat_times: Array of beat times
            base_bpm: Base tempo for comparison
            window_size: Number of beats in analysis window
            
        Returns:
            List of detected tempo changes
        """
        if len(beat_times) < window_size * 2:
            return []
        
        tempo_changes = []
        base_interval = 60.0 / base_bpm
        
        for i in range(window_size, len(beat_times) - window_size):
            # Calculate local tempo
            window_start = i - window_size // 2
            window_end = i + window_size // 2
            
            window_times = beat_times[window_start:window_end]
            if len(window_times) > 1:
                local_intervals = np.diff(window_times)
                local_bpm = 60.0 / np.mean(local_intervals)
                
                # Check for significant tempo change
                bpm_change = abs(local_bpm - base_bpm) / base_bpm
                if bpm_change > 0.1:  # 10% change threshold
                    tempo_changes.append({
                        "time": float(beat_times[i]),
                        "old_bpm": float(base_bpm),
                        "new_bpm": float(local_bpm),
                        "confidence": float(1.0 - bpm_change)
                    })
                    base_bpm = local_bpm  # Update base for next comparison
        
        return tempo_changes
    
    def create_empty_alignment_result(self) -> Dict:
        """Create empty alignment result for error cases."""
        return {
            "aligned_bpm": 120.0,
            "bpm_confidence": 0.0,
            "tempo_drift": 0.0,
            "alignment_offset": 0.0,
            "beat_grid": [],
            "tempo_changes": []
        }
    
    def create_beat_grid_from_detections(self, beat_positions: List[Dict]) -> List[Dict]:
        """Create beat grid from original detections without alignment."""
        beat_grid = []
        for i, beat in enumerate(beat_positions):
            beat_grid.append({
                "beat_time": beat["time"],
                "original_time": beat["time"],
                "correction": 0.0,
                "grid_position": i,
                "confidence": beat.get("confidence", 0.8)
            })
        return beat_grid

    def perform_tempo_alignment(self, beat_times: np.ndarray, beat_confidences: np.ndarray,
                               target_bpm: float, target_offset: float) -> Dict:
        """
        Perform tempo alignment using optimization.

        Args:
            beat_times: Detected beat times
            beat_confidences: Beat detection confidences
            target_bpm: Target BPM from TJA
            target_offset: Target offset from TJA

        Returns:
            Alignment results dictionary
        """
        target_interval = 60.0 / target_bpm

        # 1. Find optimal phase alignment
        def alignment_cost(phase_offset):
            """Cost function for phase alignment."""
            # Generate expected beat grid
            max_time = np.max(beat_times)
            expected_beats = np.arange(target_offset + phase_offset, max_time, target_interval)

            # Find closest expected beat for each detected beat
            costs = []
            for beat_time, confidence in zip(beat_times, beat_confidences):
                if len(expected_beats) > 0:
                    distances = np.abs(expected_beats - beat_time)
                    min_distance = np.min(distances)
                    # Weight by confidence and penalize large distances
                    cost = min_distance * (2.0 - confidence)
                    costs.append(cost)

            return np.mean(costs) if costs else float('inf')

        # Optimize phase offset
        phase_range = np.linspace(0, target_interval, 50)
        phase_costs = [alignment_cost(phase) for phase in phase_range]
        optimal_phase = phase_range[np.argmin(phase_costs)]

        # 2. Generate aligned beat grid
        max_time = np.max(beat_times) + target_interval
        aligned_beat_times = np.arange(target_offset + optimal_phase, max_time, target_interval)

        # 3. Match detected beats to aligned grid
        beat_grid = []
        for i, detected_time in enumerate(beat_times):
            # Find closest aligned beat
            if len(aligned_beat_times) > 0:
                distances = np.abs(aligned_beat_times - detected_time)
                closest_idx = np.argmin(distances)
                aligned_time = aligned_beat_times[closest_idx]
                correction = aligned_time - detected_time

                beat_grid.append({
                    "beat_time": float(aligned_time),
                    "original_time": float(detected_time),
                    "correction": float(correction),
                    "grid_position": int(closest_idx),
                    "confidence": float(beat_confidences[i])
                })

        # 4. Detect tempo changes
        tempo_changes = self.detect_tempo_changes(beat_times, target_bpm)

        # 5. Calculate tempo drift
        if len(beat_times) > 1:
            actual_intervals = np.diff(beat_times)
            tempo_drift = (np.std(actual_intervals) / np.mean(actual_intervals)) * 100
        else:
            tempo_drift = 0.0

        return {
            "aligned_bpm": target_bpm,
            "bpm_confidence": 1.0 - min(phase_costs) / target_interval,
            "tempo_drift": float(tempo_drift),
            "alignment_offset": float(optimal_phase),
            "beat_grid": beat_grid,
            "tempo_changes": tempo_changes
        }

    def align_tempo_with_tja(self, beat_positions: List[Dict], tja_bpm: float,
                            tja_offset: float = 0.0, tolerance: float = None) -> Dict:
        """
        Align detected beats with expected TJA tempo.

        Args:
            beat_positions: List of detected beat positions
            tja_bpm: Expected BPM from TJA file
            tja_offset: Timing offset from TJA file
            tolerance: Acceptable BPM error percentage

        Returns:
            Dictionary with alignment results
        """
        if tolerance is None:
            tolerance = self.bpm_tolerance

        if not beat_positions or tja_bpm <= 0:
            return self.create_empty_alignment_result()

        # Extract beat times and confidences
        beat_times = np.array([beat["time"] for beat in beat_positions])
        beat_confidences = np.array([beat.get("confidence", 0.8) for beat in beat_positions])

        # Calculate expected beat interval from TJA BPM
        expected_interval = 60.0 / tja_bpm

        # 1. Estimate actual BPM from detected beats
        if len(beat_times) > 1:
            beat_intervals = np.diff(beat_times)
            # Use weighted average based on confidence
            weights = (beat_confidences[:-1] + beat_confidences[1:]) / 2
            detected_bpm = 60.0 / np.average(beat_intervals, weights=weights)
        else:
            detected_bpm = tja_bpm

        # 2. Check if alignment is needed
        bpm_error = abs(detected_bpm - tja_bpm) / tja_bpm

        if bpm_error <= tolerance:
            # Beats are already well-aligned
            alignment_result = {
                "aligned_bpm": detected_bpm,
                "bpm_confidence": np.mean(beat_confidences),
                "tempo_drift": 0.0,
                "alignment_offset": 0.0,
                "beat_grid": self.create_beat_grid_from_detections(beat_positions),
                "tempo_changes": []
            }
        else:
            # Need to align beats to TJA tempo
            alignment_result = self.perform_tempo_alignment(
                beat_times, beat_confidences, tja_bpm, tja_offset
            )

        return alignment_result

    def validate_bpm_alignment(self, detected_bpm: float, tja_bpm: float,
                              segment_bpms: List[float], validation_threshold: float = None) -> Dict:
        """
        Validate BPM alignment against TJA reference.

        Args:
            detected_bpm: Average detected BPM
            tja_bpm: Expected BPM from TJA
            segment_bpms: BPM estimates from all segments
            validation_threshold: Maximum acceptable error percentage

        Returns:
            Validation results dictionary
        """
        if validation_threshold is None:
            validation_threshold = self.bpm_tolerance

        # Calculate BPM error
        bpm_error = abs(detected_bpm - tja_bpm)
        bpm_error_percentage = bpm_error / tja_bpm * 100

        # Check validation
        validation_passed = bpm_error_percentage <= (validation_threshold * 100)

        # Calculate segment consistency
        if len(segment_bpms) > 1:
            segment_consistency = 1.0 - (np.std(segment_bpms) / np.mean(segment_bpms))
        else:
            segment_consistency = 1.0

        return {
            "tja_bpm": float(tja_bpm),
            "detected_bpm": float(detected_bpm),
            "bpm_error": float(bpm_error),
            "bpm_error_percentage": float(bpm_error_percentage),
            "validation_passed": bool(validation_passed),
            "validation_threshold": float(validation_threshold * 100),
            "segment_consistency": float(max(0.0, segment_consistency))
        }

    def create_aligned_beats_output(self, alignment_result: Dict, beat_positions: List[Dict]) -> List[Dict]:
        """
        Create aligned beats output in the format expected by Phase 6.

        Args:
            alignment_result: Result from tempo alignment
            beat_positions: Original beat positions

        Returns:
            List of aligned beats with all required fields
        """
        aligned_beats = []
        beat_grid = alignment_result.get("beat_grid", [])

        for i, grid_beat in enumerate(beat_grid):
            # Find corresponding original beat
            original_beat = None
            if i < len(beat_positions):
                original_beat = beat_positions[i]

            aligned_beat = {
                "beat_id": i,
                "beat_time": grid_beat["beat_time"],
                "original_time": grid_beat["original_time"],
                "correction": grid_beat["correction"],
                "grid_position": grid_beat["grid_position"],
                "confidence": grid_beat["confidence"],
                "beat_strength": original_beat.get("strength", 1.0) if original_beat else 1.0,
                "is_downbeat": (i % 4 == 0),  # Assume 4/4 time signature
                "measure_position": i % 4,
                "bpm_at_beat": alignment_result["aligned_bpm"]
            }
            aligned_beats.append(aligned_beat)

        return aligned_beats

    def visualize_tempo_alignment(self, original_beats: List[float], aligned_beats: List[float],
                                 tja_bpm: float, song_name: str):
        """
        Visualize tempo alignment results.

        Args:
            original_beats: Original beat times
            aligned_beats: Aligned beat times
            tja_bpm: Target BPM from TJA
            song_name: Name of the song for the plot title
        """
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))

            # Plot original vs aligned beats
            ax1.scatter(original_beats, [1]*len(original_beats),
                       alpha=0.7, color='red', label='Original Beats', s=30)
            ax1.scatter(aligned_beats, [0.5]*len(aligned_beats),
                       alpha=0.7, color='blue', label='Aligned Beats', s=30)

            # Show expected beat grid
            if aligned_beats:
                expected_interval = 60.0 / tja_bpm
                expected_beats = np.arange(0, max(aligned_beats) + expected_interval, expected_interval)
                ax1.scatter(expected_beats, [0]*len(expected_beats),
                           alpha=0.5, color='green', marker='|', s=100, label='Expected Grid')

            ax1.set_title(f'Beat Alignment: {song_name} (Target BPM: {tja_bpm})')
            ax1.set_xlabel('Time (seconds)')
            ax1.set_yticks([0, 0.5, 1])
            ax1.set_yticklabels(['Expected', 'Aligned', 'Original'])
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Plot alignment corrections
            if len(original_beats) == len(aligned_beats):
                corrections = np.array(aligned_beats) - np.array(original_beats)
                ax2.plot(original_beats, corrections * 1000, 'o-', color='purple', markersize=4)
                ax2.axhline(0, color='black', linestyle='--', alpha=0.5)
                ax2.set_title('Timing Corrections Applied')
                ax2.set_xlabel('Time (seconds)')
                ax2.set_ylabel('Correction (ms)')
                ax2.grid(True, alpha=0.3)

            plt.tight_layout()

            # Save visualization
            output_path = self.output_dir / "visualizations" / f"{song_name}_alignment.png"
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

            self.logger.debug(f"Saved alignment visualization: {output_path}")

        except Exception as e:
            self.logger.warning(f"Error creating visualization for {song_name}: {e}")
            plt.close()

    def process_single_song(self, song_name: str, song_beat_files: List[Path]) -> Dict:
        """
        Process tempo alignment for a single song.

        Args:
            song_name: Name of the song
            song_beat_files: List of beat position files for this song

        Returns:
            Dictionary with processing results
        """
        try:
            # Load TJA metadata
            tja_bpm, tja_offset = self.load_tja_metadata(song_name)

            if tja_bpm is None:
                self.logger.warning(f"No TJA metadata found for {song_name}")
                return {"error": "No TJA metadata"}

            # Load all beat positions for this song
            all_beat_positions = []
            segment_bpms = []

            for beat_file in song_beat_files:
                with open(beat_file, 'r') as f:
                    beat_data = json.load(f)

                if beat_data.get("beats"):
                    all_beat_positions.extend(beat_data["beats"])
                    if beat_data.get("tempo", 0) > 0:
                        segment_bpms.append(beat_data["tempo"])

            if not all_beat_positions:
                self.logger.warning(f"No beat positions found for {song_name}")
                return {"error": "No beat positions"}

            # Perform tempo alignment
            alignment_result = self.align_tempo_with_tja(
                all_beat_positions, tja_bpm, tja_offset, self.bpm_tolerance
            )

            # Validate BPM alignment
            detected_bpm = np.mean(segment_bpms) if segment_bpms else tja_bpm
            validation_result = self.validate_bpm_alignment(
                detected_bpm, tja_bpm, segment_bpms, self.bpm_tolerance
            )

            # Create aligned beats output
            aligned_beats = self.create_aligned_beats_output(alignment_result, all_beat_positions)

            # Save results
            alignment_file = self.output_dir / "tempo_alignment" / f"{song_name}.json"
            with open(alignment_file, 'w') as f:
                json.dump(alignment_result, f, indent=2)

            validation_file = self.output_dir / "bpm_validation" / f"{song_name}.json"
            with open(validation_file, 'w') as f:
                json.dump(validation_result, f, indent=2)

            aligned_beats_file = self.output_dir / "aligned_beats" / f"{song_name}.json"
            with open(aligned_beats_file, 'w') as f:
                json.dump(aligned_beats, f, indent=2)

            # Create timing analysis
            timing_analysis = {
                "song_name": song_name,
                "tja_bpm": tja_bpm,
                "tja_offset": tja_offset,
                "detected_bpm": detected_bpm,
                "segment_count": len(song_beat_files),
                "total_beats": len(all_beat_positions),
                "alignment_quality": alignment_result["bpm_confidence"],
                "tempo_drift": alignment_result["tempo_drift"],
                "tempo_changes": len(alignment_result["tempo_changes"])
            }

            timing_file = self.output_dir / "timing_analysis" / f"{song_name}.json"
            with open(timing_file, 'w') as f:
                json.dump(timing_analysis, f, indent=2)

            # Create visualization (disabled for batch processing)
            # original_beats = [beat["time"] for beat in all_beat_positions]
            # aligned_beat_times = [beat["beat_time"] for beat in aligned_beats]
            # self.visualize_tempo_alignment(original_beats, aligned_beat_times, tja_bpm, song_name)

            return {
                "alignment": alignment_result,
                "validation": validation_result,
                "timing_analysis": timing_analysis
            }

        except Exception as e:
            error_msg = f"Error processing {song_name}: {e}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            return {"error": error_msg}

    def process_tempo_alignment(self) -> Dict:
        """Process tempo alignment for entire dataset."""

        self.logger.info("Starting Phase 5: Tempo Alignment & BPM Validation")
        start_time = time.time()

        # Find all beat position files
        beat_files = list((self.input_dir / "beat_positions").glob("*.json"))

        if not beat_files:
            self.logger.error("No beat position files found")
            return {"error": "No input files"}

        # Group beat files by song
        songs = {}
        for beat_file in beat_files:
            song_name = beat_file.stem.split("_segment_")[0]
            if song_name not in songs:
                songs[song_name] = []
            songs[song_name].append(beat_file)

        self.stats["total_songs"] = len(songs)
        self.logger.info(f"Found {len(songs)} songs with {len(beat_files)} beat files")

        all_bpm_errors = []
        all_tempo_drifts = []
        song_results = {}

        # Process each song
        for song_name, song_beat_files in tqdm(songs.items(), desc="Aligning tempo"):
            result = self.process_single_song(song_name, song_beat_files)

            if "error" in result:
                self.stats["processing_errors"].append({
                    "song": song_name,
                    "error": result["error"]
                })
                continue

            # Update statistics
            self.stats["processed_songs"] += 1

            validation = result["validation"]
            if validation["validation_passed"]:
                self.stats["validation_passed"] += 1
            else:
                self.stats["validation_failed"] += 1

            all_bpm_errors.append(validation["bpm_error_percentage"])
            all_tempo_drifts.append(result["alignment"]["tempo_drift"])

            song_results[song_name] = result

        # Calculate final statistics
        if all_bpm_errors:
            self.stats["avg_bpm_error"] = float(np.mean(all_bpm_errors))
        if all_tempo_drifts:
            self.stats["avg_tempo_drift"] = float(np.mean(all_tempo_drifts))

        # Add processing time
        processing_time = time.time() - start_time
        self.stats["processing_time_seconds"] = processing_time
        self.stats["processing_time_formatted"] = f"{processing_time/60:.1f} minutes"

        # Save comprehensive results
        with open(self.output_dir / "alignment_report.json", 'w') as f:
            json.dump(self.stats, f, indent=2)

        # Log summary
        self.logger.info(f"Phase 5 completed in {processing_time/60:.1f} minutes")
        self.logger.info(f"Processed: {self.stats['processed_songs']}/{self.stats['total_songs']} songs")
        self.logger.info(f"Validation passed: {self.stats['validation_passed']}")
        self.logger.info(f"Validation failed: {self.stats['validation_failed']}")
        self.logger.info(f"Average BPM error: {self.stats['avg_bpm_error']:.2f}%")
        self.logger.info(f"Average tempo drift: {self.stats['avg_tempo_drift']:.2f}%")

        return self.stats


# Utility functions for standalone usage
def run_phase_05(input_dir: str = "data/processed/phase4/outputs",
                output_dir: str = "data/processed/phase5",
                tja_data_dir: str = "data/raw/ese",
                bpm_tolerance: float = 0.05) -> Dict:
    """
    Run Phase 5 tempo alignment processing.

    Args:
        input_dir: Directory containing Phase 4 outputs
        output_dir: Directory for Phase 5 outputs
        tja_data_dir: Directory containing TJA files
        bpm_tolerance: Acceptable BPM error percentage

    Returns:
        Processing statistics dictionary
    """
    processor = TempoAlignmentProcessor(
        input_dir=Path(input_dir),
        output_dir=Path(output_dir),
        tja_data_dir=Path(tja_data_dir),
        bpm_tolerance=bpm_tolerance
    )

    return processor.process_tempo_alignment()


if __name__ == "__main__":
    # Run Phase 5 processing
    results = run_phase_05()
    print(f"Phase 5 completed. Processed {results['processed_songs']} songs.")
