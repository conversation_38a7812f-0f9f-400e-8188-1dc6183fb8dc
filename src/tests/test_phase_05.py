"""
Unit tests for Phase 5: Tempo Alignment & BPM Validation

Tests the core functionality of tempo alignment, BPM validation,
and tempo change detection.
"""

import unittest
import numpy as np
import tempfile
import json
from pathlib import Path
import sys

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from phases.phase_05_tempo_alignment import TempoAlignmentProcessor


class TestTempoAlignment(unittest.TestCase):
    """Test cases for tempo alignment functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.processor = TempoAlignmentProcessor(
            input_dir=self.temp_dir / "input",
            output_dir=self.temp_dir / "output",
            tja_data_dir=self.temp_dir / "tja",
            bpm_tolerance=0.05
        )
    
    def test_robust_tempo_estimation(self):
        """Test robust tempo estimation with RANSAC."""
        # Create synthetic beat times with slight timing errors
        true_bpm = 120.0
        true_interval = 60.0 / true_bpm
        
        # Perfect beats with small random errors
        perfect_beats = np.arange(0, 8, true_interval)
        noisy_beats = perfect_beats + np.random.normal(0, 0.02, len(perfect_beats))
        
        estimated_bpm = self.processor.robust_tempo_estimation(noisy_beats)
        
        # Should be close to true BPM
        self.assertAlmostEqual(estimated_bpm, true_bpm, delta=5.0)
    
    def test_tempo_alignment_no_correction_needed(self):
        """Test tempo alignment when beats are already well-aligned."""
        # Create beats that match the target BPM
        target_bpm = 120.0
        target_interval = 60.0 / target_bpm
        
        beat_positions = []
        for i in range(10):
            beat_positions.append({
                "time": i * target_interval,
                "confidence": 0.9,
                "strength": 1.0
            })
        
        result = self.processor.align_tempo_with_tja(beat_positions, target_bpm, 0.0)
        
        # Should not need significant alignment
        self.assertAlmostEqual(result["aligned_bpm"], target_bpm, delta=1.0)
        self.assertGreater(result["bpm_confidence"], 0.8)
        self.assertLess(result["tempo_drift"], 5.0)
    
    def test_tempo_alignment_with_correction(self):
        """Test tempo alignment when correction is needed."""
        # Create beats that are significantly off from target BPM (beyond tolerance)
        target_bpm = 120.0
        actual_bpm = 140.0  # 16.7% faster - beyond 5% tolerance
        actual_interval = 60.0 / actual_bpm

        beat_positions = []
        for i in range(10):
            beat_positions.append({
                "time": i * actual_interval,
                "confidence": 0.8,
                "strength": 1.0
            })

        result = self.processor.align_tempo_with_tja(beat_positions, target_bpm, 0.0)

        # Should align to target BPM when correction is needed
        self.assertAlmostEqual(result["aligned_bpm"], target_bpm, delta=0.1)
        self.assertGreater(len(result["beat_grid"]), 0)
        # Should have applied corrections
        corrections = [beat["correction"] for beat in result["beat_grid"]]
        self.assertTrue(any(abs(c) > 0.01 for c in corrections))
    
    def test_bpm_validation_pass(self):
        """Test BPM validation when alignment is good."""
        detected_bpm = 122.0
        tja_bpm = 120.0
        segment_bpms = [121.0, 122.0, 123.0, 122.5]
        
        result = self.processor.validate_bpm_alignment(
            detected_bpm, tja_bpm, segment_bpms, 0.05
        )
        
        self.assertTrue(result["validation_passed"])
        self.assertLess(result["bpm_error_percentage"], 5.0)
        self.assertGreater(result["segment_consistency"], 0.9)
    
    def test_bpm_validation_fail(self):
        """Test BPM validation when alignment is poor."""
        detected_bpm = 140.0  # 16.7% error
        tja_bpm = 120.0
        segment_bpms = [138.0, 140.0, 142.0, 141.0]
        
        result = self.processor.validate_bpm_alignment(
            detected_bpm, tja_bpm, segment_bpms, 0.05
        )
        
        self.assertFalse(result["validation_passed"])
        self.assertGreater(result["bpm_error_percentage"], 5.0)
    
    def test_tempo_change_detection(self):
        """Test tempo change detection."""
        # Create beats with a tempo change in the middle
        bpm1 = 120.0
        bpm2 = 140.0
        interval1 = 60.0 / bpm1
        interval2 = 60.0 / bpm2
        
        beat_times = []
        # First half at 120 BPM
        for i in range(10):
            beat_times.append(i * interval1)
        
        # Second half at 140 BPM
        start_time = beat_times[-1] + interval2
        for i in range(10):
            beat_times.append(start_time + i * interval2)
        
        beat_times = np.array(beat_times)
        tempo_changes = self.processor.detect_tempo_changes(beat_times, bpm1)
        
        # Should detect at least one tempo change
        self.assertGreater(len(tempo_changes), 0)
        if tempo_changes:
            # First change should be from 120 to ~140 BPM
            change = tempo_changes[0]
            self.assertAlmostEqual(change["old_bpm"], bpm1, delta=5.0)
            self.assertAlmostEqual(change["new_bpm"], bpm2, delta=10.0)
    
    def test_aligned_beats_output_format(self):
        """Test that aligned beats output has correct format."""
        beat_positions = [
            {"time": 0.5, "confidence": 0.9, "strength": 1.0},
            {"time": 1.0, "confidence": 0.8, "strength": 0.8},
            {"time": 1.5, "confidence": 0.9, "strength": 1.0}
        ]
        
        alignment_result = {
            "aligned_bpm": 120.0,
            "beat_grid": [
                {"beat_time": 0.5, "original_time": 0.5, "correction": 0.0, 
                 "grid_position": 0, "confidence": 0.9},
                {"beat_time": 1.0, "original_time": 1.0, "correction": 0.0, 
                 "grid_position": 1, "confidence": 0.8},
                {"beat_time": 1.5, "original_time": 1.5, "correction": 0.0, 
                 "grid_position": 2, "confidence": 0.9}
            ]
        }
        
        aligned_beats = self.processor.create_aligned_beats_output(
            alignment_result, beat_positions
        )
        
        # Check format
        self.assertEqual(len(aligned_beats), 3)
        
        for i, beat in enumerate(aligned_beats):
            required_fields = [
                "beat_id", "beat_time", "original_time", "correction",
                "grid_position", "confidence", "beat_strength",
                "is_downbeat", "measure_position", "bpm_at_beat"
            ]
            
            for field in required_fields:
                self.assertIn(field, beat)
            
            self.assertEqual(beat["beat_id"], i)
            self.assertEqual(beat["is_downbeat"], (i % 4 == 0))
            self.assertEqual(beat["measure_position"], i % 4)
    
    def test_empty_input_handling(self):
        """Test handling of empty or invalid inputs."""
        # Empty beat positions
        result = self.processor.align_tempo_with_tja([], 120.0, 0.0)
        self.assertEqual(result["aligned_bpm"], 120.0)
        self.assertEqual(result["bpm_confidence"], 0.0)
        
        # Invalid BPM
        beat_positions = [{"time": 0.5, "confidence": 0.9}]
        result = self.processor.align_tempo_with_tja(beat_positions, 0.0, 0.0)
        self.assertEqual(result["aligned_bpm"], 120.0)
        self.assertEqual(result["bpm_confidence"], 0.0)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


class TestTJAMetadataLoading(unittest.TestCase):
    """Test cases for TJA metadata loading."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.processor = TempoAlignmentProcessor(
            input_dir=self.temp_dir / "input",
            output_dir=self.temp_dir / "output",
            tja_data_dir=self.temp_dir / "tja",
            bpm_tolerance=0.05
        )
    
    def test_tja_metadata_loading(self):
        """Test loading BPM and offset from TJA file."""
        # Create a test TJA file
        tja_dir = self.temp_dir / "tja" / "test_song"
        tja_dir.mkdir(parents=True)
        
        tja_content = """TITLE:Test Song
BPM:120
OFFSET:-0.5
WAVE:test.ogg

#START
1111,
2222,
#END
"""
        
        tja_file = tja_dir / "test_song.tja"
        with open(tja_file, 'w', encoding='utf-8') as f:
            f.write(tja_content)
        
        bpm, offset = self.processor.load_tja_metadata("test_song")
        
        self.assertEqual(bpm, 120.0)
        self.assertEqual(offset, -0.5)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


if __name__ == "__main__":
    unittest.main()
